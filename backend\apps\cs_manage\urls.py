from django.urls import path, include
from rest_framework.routers import <PERSON><PERSON>ult<PERSON><PERSON><PERSON>
from .views.dashboard import CsManageDashboardViewSet
from .views.ai_cs_qc import CsQualityCheckViewSet, CsQualityDeductionViewSet

app_name = 'cs_manage'

# 创建路由器
router = DefaultRouter()
router.register(r'dashboard', CsManageDashboardViewSet, basename='cs_manage_dashboard')
router.register(r'cs_quality_check', CsQualityCheckViewSet, basename='cs_quality_check')
router.register(r'cs_quality_deduction', CsQualityDeductionViewSet, basename='cs_quality_deduction')

urlpatterns = [
    path('', include(router.urls)),
]
