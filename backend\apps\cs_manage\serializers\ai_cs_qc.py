from rest_framework import serializers
from dvadmin.utils.serializers import CustomModelSerializer
from apps.cs_manage.models import CsQualityCheck
import json


class CsQcExportSerializer(CustomModelSerializer):
    """
    客服质检导出序列化器
    """
    game_name = serializers.CharField(source='game.name', read_only=True, default='')
    service_performance_text = serializers.SerializerMethodField()
    deductions_text = serializers.SerializerMethodField()
    session_start_time_str = serializers.SerializerMethodField()
    session_end_time_str = serializers.SerializerMethodField()
    create_datetime_str = serializers.SerializerMethodField()
    
    class Meta:
        model = CsQualityCheck
        fields = [
            'session_id', 'service_name', 'service_id', 'service_account',
            'game_name', 'overall_score', 'total_deductions', 'session_duration',
            'session_start_time_str', 'session_end_time_str', 'create_datetime_str',
            'analysis_summary', 'deductions_text', 'service_performance_text'
        ]
    
    def get_service_performance_text(self, obj):
        """获取客服表现文本"""
        try:
            if obj.service_performance:
                performance = json.loads(obj.service_performance)
                parts = []
                if performance.get('response_speed'):
                    parts.append(f"应答速度: {performance['response_speed']}")
                if performance.get('professionalism'):
                    parts.append(f"专业程度: {performance['professionalism']}")
                if performance.get('resolution_effectiveness'):
                    parts.append(f"解决效果: {performance['resolution_effectiveness']}")
                if performance.get('service_attitude'):
                    parts.append(f"服务态度: {performance['service_attitude']}")
                return '; '.join(parts)
        except (json.JSONDecodeError, TypeError):
            pass
        return ''
    
    def get_deductions_text(self, obj):
        """获取扣分项文本"""
        deductions = obj.deductions.all()
        return '; '.join([
            f"{d.item_name}(-{d.deduction_score}分): {d.description}"
            for d in deductions
        ])
    
    def get_session_start_time_str(self, obj):
        """格式化会话开始时间"""
        return obj.session_start_time.strftime('%Y-%m-%d %H:%M:%S') if obj.session_start_time else ''
    
    def get_session_end_time_str(self, obj):
        """格式化会话结束时间"""
        return obj.session_end_time.strftime('%Y-%m-%d %H:%M:%S') if obj.session_end_time else ''
    
    def get_create_datetime_str(self, obj):
        """格式化质检时间"""
        return obj.create_datetime.strftime('%Y-%m-%d %H:%M:%S')


class CsQcListSerializer(CustomModelSerializer):
    """
    客服质检列表序列化器
    """
    game_name = serializers.CharField(source='game.name', read_only=True, default='')
    deduction_count = serializers.SerializerMethodField()
    score_level = serializers.SerializerMethodField()
    session_duration_formatted = serializers.SerializerMethodField()
    top_deductions = serializers.SerializerMethodField()
    user_info = serializers.SerializerMethodField()
    related_links_count = serializers.SerializerMethodField()
    session_summary_preview = serializers.SerializerMethodField()
    
    class Meta:
        model = CsQualityCheck
        fields = [
            'id', 'session_id', 'service_id', 'service_name', 'service_account',
            'game_name', 'overall_score', 'total_deductions', 'session_duration',
            'session_duration_formatted', 'analysis_summary', 'session_summary_preview',
            'session_start_time', 'session_end_time', 'create_datetime',
            'deduction_count', 'score_level', 'top_deductions', 'user_info', 'related_links_count'
        ]
    
    def get_deduction_count(self, obj):
        """获取扣分项数量"""
        return obj.deductions.count()

    def get_score_level(self, obj):
        """获取分数等级"""
        score = obj.overall_score
        if score >= 90:
            return {'level': 'A', 'text': '优秀', 'color': 'success'}
        elif score >= 80:
            return {'level': 'B', 'text': '良好', 'color': 'primary'}
        elif score >= 70:
            return {'level': 'C', 'text': '一般', 'color': 'warning'}
        else:
            return {'level': 'D', 'text': '待改进', 'color': 'danger'}

    def get_session_duration_formatted(self, obj):
        """格式化会话时长"""
        duration = obj.session_duration
        if duration <= 0:
            return '未知'

        hours = duration // 3600
        minutes = (duration % 3600) // 60
        seconds = duration % 60

        if hours > 0:
            return f"{hours}小时{minutes}分{seconds}秒"
        elif minutes > 0:
            return f"{minutes}分{seconds}秒"
        else:
            return f"{seconds}秒"

    def get_top_deductions(self, obj):
        """获取前3个扣分项"""
        deductions = obj.deductions.order_by('-deduction_score')[:3]
        return [
            {
                'item_name': d.item_name,
                'deduction_score': d.deduction_score,
                'category': d.category,
                'severity': d.severity
            }
            for d in deductions
        ]

    def get_user_info(self, obj):
        """获取用户信息"""
        if obj.service_user:
            return {
                'id': obj.service_user.id,
                'name': obj.service_user.name,
                'username': obj.service_user.username
            }
        return None

    def get_related_links_count(self, obj):
        """获取关联Link数量"""
        try:
            return obj.get_related_links().count()
        except Exception:
            return 0

    def get_session_summary_preview(self, obj):
        """获取会话总结预览（前100字符）"""
        if obj.session_summary:
            return obj.session_summary[:100] + ('...' if len(obj.session_summary) > 100 else '')
        return ''


class CsQcDetailSerializer(CustomModelSerializer):
    """
    客服质检详情序列化器
    """
    game_name = serializers.CharField(source='game.name', read_only=True, default='')
    service_performance_data = serializers.SerializerMethodField()
    deductions = serializers.SerializerMethodField()
    related_links = serializers.SerializerMethodField()
    user_info = serializers.SerializerMethodField()
    
    class Meta:
        model = CsQualityCheck
        fields = [
            'id', 'session_id', 'service_id', 'service_name', 'service_account',
            'game_name', 'overall_score', 'total_deductions', 'session_duration',
            'analysis_summary', 'session_summary', 'service_performance_data', 'deductions',
            'related_links', 'user_info', 'session_start_time', 'session_end_time', 'create_datetime'
        ]
    
    def get_service_performance_data(self, obj):
        """获取客服表现数据"""
        try:
            return json.loads(obj.service_performance) if obj.service_performance else {}
        except (json.JSONDecodeError, TypeError):
            return {}
    
    def get_deductions(self, obj):
        """获取扣分项详情"""
        return [
            {
                'id': d.id,
                'item_name': d.item_name,
                'category': d.category,
                'severity': d.severity,
                'description': d.description,
                'deduction_score': d.deduction_score,
                'message_index': d.message_index,
                'message_content': d.message_content,
                'suggestion': d.suggestion
            }
            for d in obj.deductions.all()
        ]

    def get_related_links(self, obj):
        """获取相关的Link记录"""
        try:
            links = obj.get_related_links()
            return [
                {
                    'id': link.id,
                    'ticket_id': link.ticket_id,
                    'link_type': link.link_type,
                    'article_id': link.article.id if link.article else None,
                    'article_title': link.article.title if link.article else None,
                    'create_datetime': link.create_datetime
                }
                for link in links
            ]
        except Exception:
            return []

    def get_user_info(self, obj):
        """获取用户信息"""
        if obj.service_user:
            return {
                'id': obj.service_user.id,
                'name': obj.service_user.name,
                'username': obj.service_user.username
            }
        return None

