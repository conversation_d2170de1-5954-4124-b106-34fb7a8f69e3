#!/usr/bin/env python3
"""
测试脚本：测试七鱼API的get_session_detail函数
使用单文件执行Django的方式
"""

import os
import sys
import django
from pathlib import Path

# 设置Django环境
def setup_django():
    """设置Django环境"""
    # 获取项目根目录
    BASE_DIR = Path(__file__).resolve().parent
    
    # 添加项目路径到sys.path
    sys.path.insert(0, str(BASE_DIR))
    
    # 设置Django设置模块
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'application.settings')
    
    # 初始化Django
    django.setup()
    
    print("✅ Django环境初始化成功")

def test_get_session_detail():
    """测试get_session_detail函数"""
    try:
        # 导入服务类
        from apps.common.qiyu_service.session_service import QiyuSessionService
        
        # 创建服务实例
        session_service = QiyuSessionService()
        
        # 测试用的会话ID（你需要替换为真实的会话ID）
        test_session_id = "11934779217"
        
        print(f"🔍 开始测试get_session_detail函数...")
        print(f"📝 测试会话ID: {test_session_id}")
        
        # 调用函数
        result = session_service.get_session_detail(test_session_id, use_cache=True)
        
        # 打印结果
        if result:
            print("✅ 获取会话详情成功!")
            print("📊 结果详情:")
            print("-" * 50)
            
            # 格式化输出结果
            import json
            formatted_result = json.dumps(result, ensure_ascii=False, indent=2)
            print(formatted_result)
            
            # 显示关键字段
            if isinstance(result, dict):
                print("\n🔑 关键字段:")
                for key in ['sessionId', 'customerId', 'staffId', 'ct', 'et']:
                    if key in result:
                        print(f"  {key}: {result[key]}")
                        
        else:
            print("❌ 获取会话详情失败或返回空结果")
            
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()

def main():
    """主函数"""
    print("🚀 开始执行七鱼API测试脚本")
    print("=" * 50)
    
    # 设置Django环境
    setup_django()
    
    # 执行测试
    test_get_session_detail()
    
    print("=" * 50)
    print("🏁 测试脚本执行完成")

if __name__ == "__main__":
    main() 