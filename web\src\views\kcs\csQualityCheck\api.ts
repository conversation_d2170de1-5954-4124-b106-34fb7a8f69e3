import { request, downloadFile } from '/@/utils/service';

const baseURL = '/api/cs_manage/cs_quality_check/';

/**
 * 客服质检相关API接口
 */

// 获取质检列表
export function getQualityCheckList(params: any) {
  return request({
    url: baseURL,
    method: 'get',
    params
  });
}

// 获取质检详情
export function getQualityCheckDetail(id: string) {
  return request({
    url: `${baseURL}${id}/`,
    method: 'get'
  });
}

// 获取看板数据
export function getDashboardData(params: any) {
  return request({
    url: `${baseURL}dashboard/`,
    method: 'get',
    params
  });
}

// 获取客服列表
export function getServiceList(params: any) {
  return request({
    url: `${baseURL}service_list/`,
    method: 'get',
    params
  });
}

// 获取客服详情
export function getServiceDetail(params: any) {
  return request({
    url: `${baseURL}service_detail/`,
    method: 'get',
    params
  });
}

// 手动质检
export function manualCheck(data: any) {
  return request({
    url: `${baseURL}manual_check/`,
    method: 'post',
    data
  });
}

// 导出数据 - 使用downloadFile处理文件下载
export function exportData(params: any = {}) {
  return downloadFile({
    url: `${baseURL}export_data/`,
    method: 'get',
    params
  });
}

// 获取扣分项统计
export function getDeductionStats(params?: any) {
  return request({
    url: '/api/cs_manage/cs_quality_deduction/statistics/',
    method: 'get',
    params
  });
}

// 获取扣分项列表
export function getDeductionList(params?: any) {
  return request({
    url: '/api/cs_manage/cs_quality_deduction/',
    method: 'get',
    params
  });
}

// 获取用户列表
export function getUserList() {
  return request({
    url: '/api/cs_manage/cs_quality_check/user_list/',
    method: 'get'
  });
}

// 根据扣分项筛选质检记录
export function getQualityCheckByDeduction(params: any) {
  return request({
    url: baseURL + 'dashboard/',
    method: 'get',
    params: {
      ...params,
      deduction_item: params.deduction_item
    }
  });
}