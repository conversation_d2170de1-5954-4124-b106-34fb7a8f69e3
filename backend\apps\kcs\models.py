from datetime import datetime, timedelta

from django.db import models
from dvadmin.utils.models import CoreModel, table_prefix
from dvadmin.system.models import Users

table_prefix = table_prefix + "kcs_"


class ArticleCategory(CoreModel):
    name = models.CharField(max_length=255, verbose_name="分类名称", help_text="分类名称")

    class Meta:
        db_table = table_prefix + "article_category"
        verbose_name = "文章分类表"
        verbose_name_plural = verbose_name


class LinkCategory(CoreModel):
    name = models.CharField(max_length=255, verbose_name="分类名称", help_text="分类名称")

    class Meta:
        db_table = table_prefix + "link_category"
        verbose_name = "link分类表"
        verbose_name_plural = verbose_name


class Game(CoreModel):
    name = models.CharField(max_length=255, verbose_name="游戏名称", help_text="游戏名称")
    game_id = models.Char<PERSON>ield(max_length=255, verbose_name="游戏ID", help_text="游戏ID", default="")
    img = models.CharField(max_length=255, verbose_name="图标", help_text="图标", null=True, blank=True)

    class Meta:
        db_table = table_prefix + "game"
        verbose_name = "游戏表"
        verbose_name_plural = verbose_name


class Article(CoreModel):
    game = models.ForeignKey(Game, on_delete=models.CASCADE, verbose_name="游戏ID", help_text="游戏ID", default=None)
    title = models.CharField(max_length=255, verbose_name="标题", help_text="标题")
    summary = models.CharField(max_length=255, verbose_name="摘要", help_text="摘要")
    content = models.TextField(verbose_name="内容", help_text="内容")
    tags = models.JSONField(default=list, blank=True, null=True, verbose_name="文章标签", help_text="由LLM生成的文章标签列表")
    category = models.ForeignKey(ArticleCategory, on_delete=models.CASCADE, verbose_name="分类", help_text="分类",
                                 default=None)
    problem_type = models.CharField(max_length=255, verbose_name="问题类型", help_text="问题类型", blank=True)
    is_top = models.BooleanField(default=False, verbose_name="是否置顶", help_text="是否置顶")
    is_review = models.BooleanField(default=False, verbose_name="是否审核", help_text="是否审核")
    is_active = models.BooleanField(default=True, verbose_name="文章活跃", help_text="文章活跃")
    review_time = models.DateTimeField(null=True, blank=True, verbose_name="审核时间", help_text="审核时间")
    status = models.CharField(max_length=255, verbose_name="状态", help_text="状态")
    alarm = models.DateTimeField(null=True, blank=True, verbose_name="告警时间", help_text="告警时间")
    sort = models.IntegerField(default=0, verbose_name="排序", help_text="排序")
    reward_points = models.IntegerField(default=0, verbose_name="奖励积分", help_text="奖励积分")

    # 设置一个功能，当更新is_review字段状态时，自动更新review_time字段
    def save(self, *args, **kwargs):
        # 如果是审核状态，更新审核时间
        if self.is_review:
            self.review_time = self.update_datetime

        # 调用原始的save方法
        super().save(*args, **kwargs)

    class Meta:
        db_table = table_prefix + "article"
        verbose_name = "文章表"
        verbose_name_plural = verbose_name
        indexes = [
            models.Index(fields=['is_active']),
            models.Index(fields=['is_top']),
            models.Index(fields=['category']),
            models.Index(fields=['game']),
            models.Index(fields=['sort']),
            models.Index(fields=['create_datetime']),
        ]


class Link(CoreModel):
    article = models.ForeignKey(Article, on_delete=models.CASCADE, verbose_name="文章ID", help_text="文章ID",
                                error_messages={'unique': '无法重复关联'}, default=None)
    ticket_id = models.CharField(max_length=512, verbose_name="ticketId", help_text="ticketId", default="")
    link_type = models.CharField(max_length=255, verbose_name="链接类型", help_text="链接类型", default=1)

    class Meta:
        db_table = table_prefix + "link"
        verbose_name = "link表"
        verbose_name_plural = verbose_name
        unique_together = ('article', 'ticket_id')
        indexes = [
            models.Index(fields=['article', 'ticket_id', 'creator']),
        ]


class ArticleComments(CoreModel):
    article = models.ForeignKey(Article, on_delete=models.CASCADE, verbose_name="文章ID", help_text="文章ID",
                                default=None)
    content = models.TextField(verbose_name="内容", help_text="内容")
    parent = models.ForeignKey('self', on_delete=models.CASCADE, null=True, blank=True, verbose_name="父评论ID",
                               help_text="父评论ID", default=None)
    is_active = models.BooleanField(default=True, verbose_name="是否活跃", help_text="是否活跃")
    likes = models.IntegerField(default=0, verbose_name="点赞数", help_text="点赞数")

    class Meta:
        db_table = table_prefix + "article_comments"
        verbose_name = "文章评论表"
        verbose_name_plural = verbose_name
        indexes = [
            models.Index(fields=['article']),
            models.Index(fields=['parent']),
            models.Index(fields=['is_active']),
        ]


class ArticleCommentsLikes(CoreModel):
    comment = models.ForeignKey(ArticleComments, on_delete=models.SET_NULL, verbose_name="评论ID", help_text="评论ID",
                                null=True, blank=True, default=None)

    class Meta:
        db_table = table_prefix + "article_comment_likes"
        verbose_name = "文章评论点赞表"
        verbose_name_plural = verbose_name
        unique_together = ('comment', 'creator')
        indexes = [
            models.Index(fields=['comment']),
        ]


class UserPointsLogs(CoreModel):
    user = models.ForeignKey(Users, on_delete=models.SET_NULL, related_name='points_logs', verbose_name="用户ID",
                             help_text="用户ID", null=True, blank=True, default=None)
    points = models.IntegerField(default=0, verbose_name="积分", help_text="积分")
    type = models.CharField(max_length=255, verbose_name="类型", help_text="类型", default=1,
                            choices=[(0, "remove"), (1, "add")])
    comment = models.ForeignKey(ArticleComments, on_delete=models.SET_NULL, null=True, blank=True,
                                verbose_name="评论ID", help_text="评论ID", default=None)
    reason = models.CharField(max_length=255, verbose_name="原因", help_text="原因", default="")

    class Meta:
        db_table = table_prefix + "user_points_logs"
        verbose_name = "用户积分日志表"
        verbose_name_plural = verbose_name
        indexes = [
            models.Index(fields=['user']),
            models.Index(fields=['type']),
        ]

class AICsPerformanceAnalysis(CoreModel):
    link = models.OneToOneField(Link, on_delete=models.CASCADE, verbose_name="关联Link", help_text="触发分析的Link记录", related_name='performance_analysis')
    session_id = models.CharField(max_length=512, verbose_name="会话ID", help_text="七鱼会话ID", db_index=True)
    initial_sentiment_score = models.FloatField(null=True, blank=True, verbose_name="初始情绪得分", help_text="LLM评估的对话开始时用户情绪分 (-10 到 10)")
    final_sentiment_score = models.FloatField(null=True, blank=True, verbose_name="最终情绪得分", help_text="LLM评估的对话结束时用户情绪分 (-10 到 10)")
    sentiment_change_score = models.FloatField(null=True, blank=True, verbose_name="情绪变化得分", help_text="最终情绪得分 - 初始情绪得分")
    analysis_details = models.JSONField(null=True, blank=True, verbose_name="LLM分析详情", help_text="LLM返回的完整分析结果或理由")

    class Meta:
        db_table = table_prefix + "cs_performance_analysis"
        verbose_name = "客服表现分析"
        verbose_name_plural = verbose_name
        indexes = [
            models.Index(fields=['session_id']),
            models.Index(fields=['create_datetime']),
        ]






class CsEmotionAnalysis(CoreModel):
    """客服会话情绪分析主表"""
    session_id = models.CharField(max_length=512, verbose_name="会话ID", help_text="七鱼会话ID", db_index=True, unique=True)
    
    # 客服信息
    service_id = models.CharField(max_length=255, verbose_name="客服ID", help_text="客服ID", db_index=True)
    service_name = models.CharField(max_length=255, verbose_name="客服姓名", help_text="客服姓名")
    service_account = models.CharField(max_length=255, verbose_name="客服账号", help_text="客服账号", null=True, blank=True)
    service_user = models.ForeignKey(
        Users,
        on_delete=models.SET_NULL,
        verbose_name="关联系统用户",
        help_text="通过姓名关联的系统用户",
        null=True, blank=True,
        related_name='emotion_analyses'
    )
    
    # 关联信息
    game = models.ForeignKey(Game, on_delete=models.SET_NULL, verbose_name="游戏", help_text="关联游戏", null=True, blank=True)
    link = models.ForeignKey(Link, on_delete=models.SET_NULL, verbose_name="关联Link", help_text="关联的文章链接", null=True, blank=True, related_name='emotion_analysis')
    
    # 情绪分析结果
    initial_emotion_score = models.FloatField(verbose_name="初始情绪分数", help_text="用户初始情绪分数(-10到10)", null=True, blank=True)
    initial_emotion_justification = models.TextField(verbose_name="初始情绪分析理由", help_text="LLM分析初始情绪的理由", blank=True)
    
    final_emotion_score = models.FloatField(verbose_name="最终情绪分数", help_text="用户最终情绪分数(-10到10)", null=True, blank=True)
    final_emotion_justification = models.TextField(verbose_name="最终情绪分析理由", help_text="LLM分析最终情绪的理由", blank=True)
    
    emotion_change_score = models.FloatField(verbose_name="情绪变化分数", help_text="最终情绪分数 - 初始情绪分数", null=True, blank=True)
    overall_assessment = models.TextField(verbose_name="整体评估", help_text="LLM对客服表现的整体评估", blank=True)
    
    # 会话统计信息
    session_start_time = models.DateTimeField(null=True, blank=True, verbose_name="会话开始时间")
    session_end_time = models.DateTimeField(null=True, blank=True, verbose_name="会话结束时间")
    session_duration = models.IntegerField(default=0, verbose_name="会话时长", help_text="会话时长(秒)")
    message_count = models.IntegerField(default=0, verbose_name="消息总数", help_text="会话中的消息总数")
    user_message_count = models.IntegerField(default=0, verbose_name="用户消息数", help_text="用户发送的消息数")
    service_message_count = models.IntegerField(default=0, verbose_name="客服消息数", help_text="客服发送的消息数")
    
    # 分析详情和元数据
    analysis_details = models.JSONField(null=True, blank=True, verbose_name="完整分析结果", help_text="LLM返回的完整分析结果")
    conversation_summary = models.TextField(verbose_name="会话摘要", help_text="会话内容摘要", blank=True)
    emotion_keywords = models.JSONField(default=list, blank=True, verbose_name="情绪关键词", help_text="提取的情绪相关关键词")
    structured_conversation = models.JSONField(null=True, blank=True, verbose_name="结构化会话数据", help_text="用户问题、客服行为、解决方案等结构化数据")
    
    # 处理状态
    STATUS_CHOICES = [
        ('pending', '待处理'),
        ('processing', '处理中'),
        ('completed', '已完成'),
        ('failed', '处理失败'),
    ]
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending', verbose_name="处理状态")
    error_message = models.TextField(verbose_name="错误信息", help_text="处理失败时的错误信息", blank=True)
    
    class Meta:
        db_table = table_prefix + "cs_emotion_analysis"
        verbose_name = "客服会话情绪分析"
        verbose_name_plural = verbose_name
        indexes = [
            models.Index(fields=['session_id']),
            models.Index(fields=['service_id']),
            models.Index(fields=['emotion_change_score']),
            models.Index(fields=['create_datetime']),
            models.Index(fields=['game']),
            models.Index(fields=['status']),
            models.Index(fields=['session_start_time']),
        ]

    def get_current_link(self):
        """
        获取当前关联的Link（软关联）
        优先返回硬关联，无则动态查找
        """
        if self.link:
            return self.link
        
        try:
            return Link.objects.filter(ticket_id=self.session_id).first()
        except Exception:
            return None


    def get_all_related_links(self):
        """
        获取所有相关的Link记录
        """
        return Link.objects.filter(ticket_id=self.session_id)

    def refresh_associations(self):
        """
        刷新关联关系（将软关联的记录转为硬关联）
        当有新的Link或质检记录创建后，可调用此方法更新硬关联
        
        Returns:
            bool: 是否有更新
        """
        updated = False
        update_fields = []
        
        # 更新Link关联
        if not self.link:
            current_link = self.get_current_link()
            if current_link and current_link != self.link:
                self.link = current_link
                update_fields.append('link')
                updated = True
        

        # 保存更新
        if updated:
            self.save(update_fields=update_fields)
            
        return updated

    @property 
    def related_link(self):
        """
        软关联Link属性（优先硬关联，无则动态查找）
        """
        return self.get_current_link()



class CsEmotionAnalysisDetail(CoreModel):
    """客服会话情绪分析详情表 - 存储分阶段的情绪分析"""
    emotion_analysis = models.ForeignKey(
        CsEmotionAnalysis,
        on_delete=models.CASCADE,
        related_name='details',
        verbose_name="情绪分析记录"
    )
    
    # 分析阶段
    PHASE_CHOICES = [
        ('initial', '初始阶段'),
        ('middle', '中间阶段'),
        ('final', '最终阶段'),
    ]
    phase = models.CharField(max_length=20, choices=PHASE_CHOICES, verbose_name="分析阶段")
    
    # 时间范围
    start_message_index = models.IntegerField(verbose_name="起始消息索引", help_text="该阶段分析的起始消息索引")
    end_message_index = models.IntegerField(verbose_name="结束消息索引", help_text="该阶段分析的结束消息索引")
    
    # 情绪分析结果
    emotion_score = models.FloatField(verbose_name="情绪分数", help_text="该阶段的情绪分数(-10到10)")
    emotion_description = models.CharField(max_length=100, verbose_name="情绪描述", help_text="情绪的文字描述")
    confidence_level = models.FloatField(verbose_name="置信度", help_text="分析结果的置信度(0-1)", default=0.5)
    
    # 关键证据
    key_messages = models.JSONField(default=list, verbose_name="关键消息", help_text="影响情绪判断的关键消息")
    emotion_triggers = models.JSONField(default=list, verbose_name="情绪触发因素", help_text="导致情绪变化的触发因素")
    
    class Meta:
        db_table = table_prefix + "cs_emotion_analysis_detail"
        verbose_name = "客服会话情绪分析详情"
        verbose_name_plural = verbose_name
        indexes = [
            models.Index(fields=['emotion_analysis', 'phase']),
            models.Index(fields=['emotion_score']),
        ]
        unique_together = ('emotion_analysis', 'phase')