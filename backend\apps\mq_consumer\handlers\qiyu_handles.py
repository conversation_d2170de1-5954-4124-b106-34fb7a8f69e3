import logging
import json
from django.core.cache import cache
from apps.mq_consumer.decorators import register_handler
from apps.kcs.tasks import task__ai_cs_emotion_analysis
from apps.cs_manage.tasks import LLM_auto_qc

logger = logging.getLogger(__name__)

@register_handler(
        topic='qiyu_session_end_topic_no_ai', 
        tags='*',
        description='会话关闭事件处理器',
        group_id='CS_ADMIN_SESSION_CLOSED_GROUP'
        )
def qiyu_session_end_topic_no_ai(
                        msg, 
                        log_entry,
                        evaluation=100
                        ):
    """
    处理会话闭合事件，自动关联相关文章
    
    参数说明：
    - min_confidence: 文章关联的最小置信度阈值
    - max_links: 最多关联的文章数量
    """
    logger.info(f"Handling 'SessionClosed' message: {msg}")
    try:
        mq_body = json.loads(msg.body.decode('utf-8'))
        session_request = mq_body.get('request')
        session_id = session_request.get('sessionId')

        
        if not session_id:
            logger.error("消息中缺少sessionId字段",mq_body)
            return True
            
        logger.info(f"收到会话闭合事件，会话ID: {session_id}")
        
        # 触发异步任务，处理客服会话质检
        LLM_auto_qc.delay(
            session_id=session_id, 
            evaluation=evaluation
        )

        # 异步任务，处理客服会话的情绪分析
        task__ai_cs_emotion_analysis.delay(
            session_id=session_id
        )
        
        # 消息处理成功
        return True
        
    except Exception as e:
        logger.error(f"消费失败: {e}")
        return True  # 返回True以确认消息，避免重复处理 