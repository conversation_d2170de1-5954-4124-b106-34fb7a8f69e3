# -*- coding: utf-8 -*-

"""
@author: 阿辉
@contact: QQ:2655399832
@Created on: 2022/9/21 16:30
@Remark:
"""
import json
import logging
import math

from django.db.models.functions import Coalesce

from apps.kcs.models import *
from apps.kcs.views.article import Article
from django.db.models import OuterRef, Count, Subquery, Value
from apps.common.fs_send_message import *
from apps.common.qiyu_service import get_qiyu_service
import datetime
import time
from django.db import transaction
from apps.common.fs_table import FeishuBitableManager
from apps.common.LLM_api import LLM_api
from apps.kcs.utils.ai_cs_performance_analysis import ai_cs_performance_analysis

logger = logging.getLogger(__name__)


def get_report_data(start, end, link_type=None, game_name=None):
    results = {}
    start_time_s = int(time.mktime(start.timetuple())) * 1000
    end_time_s = int(time.mktime(end.timetuple())) * 1000
    qiyu_service = get_qiyu_service()
    # 获取七鱼overviews
    overviews = qiyu_service.get_statistics_overview(start_time_s, end_time_s)
    overviews = overviews['result']
    results['sessions'] = overviews['effectSessions']  # 会话数
    # 判断start, end的间隔是否大于一天
    if (end - start).days > 1:
        # 获取这段时间的link数据
        link_filter = {
            'create_datetime__gte': start,
            'create_datetime__lte': end
        }
        # 仅当link_type有值时添加筛选条件
        if link_type is not None:
            link_filter['link_type'] = link_type
            
        link_queryset = Link.objects.filter(**link_filter)
        # totalSessionCount 是这段时间的会话数。link_queryset.count()是这段时间的link数
        # 计算Link率
        results['link_rate'] = str(round(link_queryset.count() / results['sessions'] * 100, 2)) + '%' if results['sessions'] > 0 else 0
        results['link_count'] = link_queryset.count()
    else:
        # 获取七鱼在线工单
        all_session = qiyu_service.get_session_history(start_time_s, end_time_s)
        session_id_list = [str(session['id']) for session in all_session]
        # 获取时间段内 link库中的数据
        link_filter = {
            'create_datetime__gte': start,
            'create_datetime__lte': end
        }
        # 仅当link_type有值时添加筛选条件
        if link_type is not None:
            link_filter['link_type'] = link_type
            
        link_queryset = Link.objects.filter(**link_filter)

        # 根据session_id_list中的数据，和link_queryset中的ticket_id进行比对，计算出Link率。每个session_id都应该对一个link,否则就是未关联的工单
        session_link_map = {session_id: False for session_id in session_id_list}
        # 遍历 link_queryset，检查每个 link 的 ticket_id 是否在 session_id_list 中
        for link in link_queryset:
            ticket_id = str(link.ticket_id)
            if ticket_id in session_id_list:
                session_link_map[ticket_id] = True
        # 计算未关联的工单数量
        unlinked_sessions = [session_id for session_id, linked in session_link_map.items() if not linked]
        unlinked_count = len(unlinked_sessions)
        # 计算 Link 率
        total_sessions = len(session_id_list)
        results['link_rate'] = str(
            round((total_sessions - unlinked_count) / total_sessions * 100, 2)) + '%' if total_sessions > 0 else 0

        #    获取Link_queryset的总数
        results['link_count'] = link_queryset.count()

    results['satisfactionRatio'] = str(round(overviews['satisfactionRatio'] * 100, 2)) + '%'  # 满意度
    results['oneOffRatio'] = str(round(overviews['oneOffRatio'] * 100, 2)) + '%'  # 一次性解决率
    results['specialAnswerRatio'] = str(round(overviews['specialAnswerRatio'] * 100, 2)) + '%'  # 特殊回答率
    results['assignedRatio'] = str(round(overviews['assignedRatio'] * 100, 2)) + '%'  # 接入率
    # 时间戳毫秒数，转为分钟时间
    results['avgFirstRespTime'] = str(overviews['avgFirstRespTime'] // 1000) + '秒'  # 平均首次响应时长

    # 获取article top 10
    # 构建文章链接的筛选条件
    article_link_filter = {
        'article_id': OuterRef('pk'),
        'create_datetime__gte': start,
        'create_datetime__lte': end
    }
    # 仅当link_type有值时添加筛选条件
    if link_type is not None:
        article_link_filter['link_type'] = link_type
        
    article_queryset = Article.objects.annotate(
        link_count=Subquery(
            Link.objects.filter(
                **article_link_filter
            ).values('article_id').annotate(count=Count('id')).values('count')
        )
    ).order_by('-link_count')
    
    # 仅当game_name有值时添加筛选条件
    if game_name:
        article_queryset = article_queryset.filter(game__name=game_name)
        
    results['article_knowledge_top_10'] = [
        {'title': article.title, 'link_count': article.link_count, 'id': article.id, 'game_name': article.game.name} for
        article in article_queryset.filter(category__name="同事知识")[:10]]
    results['article_policy_top_10'] = [
        {'title': article.title, 'link_count': article.link_count, 'id': article.id, 'game_name': article.game.name} for
        article in article_queryset.filter(category__name="流程")[:10]]

    return results


@app.task
def task__dynamic_task(*args, **kwargs):
    func = kwargs.get('func', '')
    print(func)
    exec(f"""{func}""")


@app.task
def task__article_review(*args, **kwargs):
    hour_config = kwargs.get('hour_config', {"hour": 24, "count": 5})
    now = datetime.datetime.now()
    start_time = now - datetime.timedelta(hours=hour_config['hour'])
    end_time = now

    # 获取article对象 要关联他的link的count值, 获取create_datetime在start_time和end_time之间的数据
    # review time必须是空的
    articles = Article.objects.filter(create_datetime__gte=start_time,
                                      create_datetime__lte=end_time,
                                      is_active=True, review_time=None
                                      ).annotate(
        link_count=Coalesce(
            Subquery(
                Link.objects.filter(
                    article_id=OuterRef('pk'),
                    create_datetime__gte=start_time,
                    create_datetime__lte=end_time
                ).values('article_id').annotate(count=Count('id')).values('count')
            ),
            Value(0)
        )
    )
    # 判断article的link_count是否大于hour_config['count']，如果小于就设置is_review为True
    update_articles = []
    for article in articles:
        if article.link_count >= int(hour_config['count']):
            article.is_review = True
            article.description = f"Link数量达到{article.link_count}"
            update_articles.append(article)
    if update_articles:
        with transaction.atomic():
            Article.objects.bulk_update(update_articles, ['is_review', 'description'])

    top_review_configs = kwargs.get('review_configs', [])
    for config in top_review_configs:
        days = config['days']
        top = config['top']
        start_time = now - datetime.timedelta(days=days)
        top_articles = Article.objects.annotate(
            link_count=Coalesce(
                Subquery(
                    Link.objects.filter(
                        article_id=OuterRef('pk'),
                        create_datetime__gte=start_time,
                        create_datetime__lte=end_time
                    ).values('article_id').annotate(count=Count('id')).values('count')
                ),
                Value(0)
            )
        ).order_by('-link_count')[:top]
        for article in top_articles:
            if not article.link_count:
                continue

            # 判断,如果article的review_time 存在,那么就计算他距今多少天.如果距今的天数大于days,就设置is_review为True
            if article.review_time:
                if (now - article.review_time).days > days:
                    article.is_review = True
                    article.description = f"Top {top} 文章, Link达到 {article.link_count}"
                    with transaction.atomic():
                        article.save()

            else:
                article.is_review = True
                article.description = f"Top {top} 文章, Link达到 {article.link_count}"
                with transaction.atomic():
                    article.save()


@app.task
@app.logger
def task__daily_report(*args, **kwargs):
    period = kwargs.get('period', 'Daily')
    recivers = kwargs.get('reciver', [])
    link_type = kwargs.get('link_type') # 不设置默认值，由get_report_data使用其默认值
    game_name = kwargs.get('game_name')
    now = datetime.datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
    delta_days = {
        'Daily': 1,
        'Weekly': 7,
        'Monthly': 30
    }
    if period in delta_days:
        start_time = now - datetime.timedelta(days=delta_days[period])
    else:
        start_time = now - datetime.timedelta(days=1)  # 默认是昨天
    end_time = now
    result = get_report_data(start_time, end_time, link_type, game_name)
    fs_card_id = 'AAqkod7bqO0SS'
    card_data = {
        "type": "template",
        "data": {
            "template_id": fs_card_id,
            "template_variable": {
                "title": f"{game_name} {period} Report" if game_name else f"{period} Report",
                "start_time": start_time.strftime('%Y-%m-%d'),
                "end_time": end_time.strftime('%Y-%m-%d'),
                "totalLink": result['link_count'],
                "linkRate": result['link_rate'],
                "sessionCount": result['sessions'],
                "sessionRate": result['satisfactionRatio'],
                "moreDetail": f"""接入率 {result['assignedRatio']}
                    平均首响 {result['avgFirstRespTime']}
                    30s应答率 {result['specialAnswerRatio']}
                    一次性解决率 {result['oneOffRatio']}""".replace(" ", "")
            }
        }
    }
    for index, item in enumerate(result['article_knowledge_top_10']):
        rich_text = f"<text_tag color='green'>{item['id']}</text_tag> <a href='https://csadmin.ztgame.com/#/kcs/article?id={item['id']}'>[{item['game_name']}] {item['title']}</a>"
        link_rich_text = f"<a href='https://csadmin.ztgame.com/#/kcs/link?article={item['id']}'>{item['link_count'] if item['link_count'] else 0} links</a>"
        card_data['data']['template_variable'][f"article{index + 1}"] = rich_text
        card_data['data']['template_variable'][f"a{index + 1}Link"] = link_rich_text

    for index, item in enumerate(result['article_policy_top_10']):
        rich_text = f"<text_tag color='green'>{item['id']}</text_tag> <a href='https://csadmin.ztgame.com/#/kcs/article?id={item['id']}'>[{item['game_name']}] {item['title']}</a>"
        link_rich_text = f"<a href='https://csadmin.ztgame.com/#/kcs/link?article={item['id']}'>{item['link_count'] if item['link_count'] else 0} links</a>"
        card_data['data']['template_variable'][f"policy{index + 1}"] = rich_text
        card_data['data']['template_variable'][f"p{index + 1}Link"] = link_rich_text

    result_text = json.dumps(card_data, ensure_ascii=False).replace('"', '\"')
    app_push_msg(
        recivers,
        'g',
        'interactive',
        result_text
    )


def safe_division(num1, num2):
    return num1 / num2 if num2 != 0 else 0


@app.task(bind=True, max_retries=3, ignore_result=True)
def task__article_rebuild_sort(*args, **kwargs):
    """
    计算文章热度分数任务

    根据多种因素计算文章的热度分数，并更新排序值：
    1. 基础分：文章在时间窗口内的链接总数
    2. 时间因子：最近时间内的活跃度
    3. 评论因子：文章的评论数量
    4. 时效性：文章创建时间的新鲜度

    可通过kwargs传入以下参数：
    - long_window_days: 长时间窗口天数，默认7天
    - short_window_hours: 短时间窗口小时数，默认24小时
    - base_weight: 基础分权重，默认100
    - time_weight: 时间因子权重，默认200
    - comment_weight: 评论因子权重，默认50
    - freshness_weight: 时效性权重，默认150
    - batch_size: 批量更新大小，默认500
    """
    # 获取配置参数
    long_window_days = kwargs.get('long_window_days', 7)
    short_window_hours = kwargs.get('short_window_hours', 24)
    base_weight = kwargs.get('base_weight', 100)
    time_weight = kwargs.get('time_weight', 200)
    comment_weight = kwargs.get('comment_weight', 50)
    freshness_weight = kwargs.get('freshness_weight', 150)
    batch_size = kwargs.get('batch_size', 500)

    now = datetime.datetime.now()
    long_window_start = now - timedelta(days=long_window_days)
    short_window_start = now - timedelta(hours=short_window_hours)

    logger.info(f"开始计算文章热度，长时间窗口: {long_window_days}天，短时间窗口: {short_window_hours}小时")
    start_time = time.time()

    try:
        # 获取活跃文章
        active_articles = Article.objects.filter(is_active=True)
        total_articles = active_articles.count()
        logger.info(f"找到 {total_articles} 篇活跃文章")

        # 获取链接数据
        links_data = Link.objects.filter(
            create_datetime__range=(long_window_start, now)
        ).values('article_id', 'create_datetime')

        # 统计总链接数
        total_links = len(links_data)
        recent_links = sum(1 for link in links_data if link['create_datetime'] >= short_window_start)

        logger.info(f"找到 {total_links} 个链接数据，其中最近 {short_window_hours} 小时内有 {recent_links} 个")

        # 创建文章ID到链接计数的映射
        article_link_counts = {}
        article_recent_counts = {}

        for link in links_data:
            article_id = link['article_id']
            create_time = link['create_datetime']

            # 统计长时间窗口内的链接数
            if article_id not in article_link_counts:
                article_link_counts[article_id] = 0
            article_link_counts[article_id] += 1

            # 统计短时间窗口内的链接数
            if create_time >= short_window_start:
                if article_id not in article_recent_counts:
                    article_recent_counts[article_id] = 0
                article_recent_counts[article_id] += 1

        # 获取评论数据
        comments_data = ArticleComments.objects.filter(
            is_active=True
        ).values('article_id').annotate(
            comment_count=Count('id')
        )

        # 创建文章ID到评论计数的映射
        article_comment_counts = {item['article_id']: item['comment_count'] for item in comments_data}

        # 分批处理文章
        articles_to_update = []
        processed_count = 0

        for i in range(0, total_articles, batch_size):
            batch = active_articles[i:i + batch_size]

            for article in batch:
                try:
                    # 获取文章的链接计数
                    link_count = article_link_counts.get(article.id, 0)
                    recent_link_count = article_recent_counts.get(article.id, 0)
                    comment_count = article_comment_counts.get(article.id, 0)

                    # 计算时效性因子 - 使用指数衰减函数
                    days_old = (now - article.create_datetime).days
                    freshness_factor = math.exp(-days_old / 30)  # 30天后降低一半

                    # 计算热度分数的各个组成部分
                    # 1. 基础分：链接数 * 权重
                    base_score = link_count * base_weight

                    # 2. 时间活跃度：最近短时间窗口的活跃度
                    time_factor = (recent_link_count / max(1, recent_links)) * time_weight

                    # 3. 评论因子：评论数 * 权重
                    comment_factor = math.log(1 + comment_count) * comment_weight

                    # 4. 时效性：新鲜度 * 权重
                    freshness_score = freshness_factor * freshness_weight

                    # 最终热度分数 = 基础分 + 时间因子 + 评论因子 + 时效性
                    hotness = base_score + time_factor + comment_factor + freshness_score

                    # 转换为整数排序值，确保至少为1
                    sort_value = max(1, round(hotness))

                    # 仅当排序值变化时才更新
                    if article.sort != sort_value:
                        article.sort = sort_value
                        articles_to_update.append(article)

                except Exception as e:
                    logger.error(f"处理文章 {article.id} 时出错: {str(e)}", exc_info=True)

            processed_count += len(batch)
            logger.info(f"已处理 {processed_count}/{total_articles} 篇文章")

        # 使用批量更新
        if articles_to_update:
            with transaction.atomic():
                # 使用单独更新替代批量更新，确保传递update_fields参数
                for article in articles_to_update:
                    article.save(update_fields=['sort'])

        execution_time = time.time() - start_time
        logger.info(f"文章热度计算完成，共更新 {len(articles_to_update)} 篇文章，耗时 {execution_time:.2f} 秒")

    except Exception as e:
        logger.error(f"文章热度计算任务失败: {str(e)}", exc_info=True)
        raise  # 重新抛出异常，让Celery可以处理重试



@app.task(ignore_result=True)
def task__top_article_to_feishu():
    app_id = settings.FEISHU_APP_ID
    app_secret = settings.FEISHU_APP_SECRET
    table_app_token = "EOEKbZtQDas5a2sCTPYc1RO1nkd"
    fs_table = FeishuBitableManager(app_id, app_secret, table_app_token)
    # # 获取所有游戏名称
    games = Game.objects.all()
    game_names = [game.name for game in games]
    # # # 确保每个游戏都有一个对应的数据表
    # table_fields = [
    #                 {
    #                     "field_name": "ID",
    #                     "type": 1
    #                 },
    #                 {
    #                     "field_name": "Title",
    #                     "type": 1
    #                 },
    #                 {
    #                     "field_name": "Summary",
    #                     "type": 1
    #                 },
    #                 {
    #                     "field_name": "Category",
    #                     "type": 1
    #                 },
    #                 {
    #                     "field_name": "LinkCount",
    #                     "type": 1
    #                 }
    #             ]
    # for game in game_names:
    #     fs_table.ensure_table(table_name=game,table_fields=table_fields)

    # 获取过去24小时的时间范围
    # now = 今天的0点
    now = datetime.datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
    start_time = now - datetime.timedelta(hours=24)
    end_time = now

    # 获取过去24小时内的文章及其链接数
    link_counts = Link.objects.filter(
        create_datetime__gte=start_time,
        create_datetime__lte=end_time,
    ).values('article_id').annotate(link_count=Count('id'))

    articles_with_links = Article.objects.annotate(
        link_count=Subquery(
            link_counts.filter(article_id=OuterRef('pk')).values('link_count')
        )
    )

    # 使用 article_queryset 根据 game 的 name 进行分组，并根据 category__name 再次进行分组，取出每个 category 下的前10篇文章
    top_articles = {}
    for game in game_names:
        articles = articles_with_links.filter(game__name=game).order_by('-link_count')
        category_articles = {}
        for article in articles:
            category_name = article.category.name
            if category_name not in category_articles:
                category_articles[category_name] = []
            category_articles[category_name].append(article)
        top_articles[game] = {}
        for category_name, articles in category_articles.items():
            top_articles[game][category_name] = articles[:10]

    # 打印结果
    for game, categories in top_articles.items():
        records = []
        for category_name, articles in categories.items():
            for article in articles:
                # 如果文章没有链接数，则跳过
                if not article.link_count:
                    continue
                record = {
                    "ID": str(article.id),
                    "Title": str(article.title),
                    "Summary": str(article.summary),
                    "Category": str(category_name),
                    "LinkCount": str(article.link_count),
                }
                records.append(record)

        if not records:
            print(f"No top articles found for game '{game}'.")
            continue

        table_id = fs_table.get_tables()
        table_dict = {table['name']: table['table_id'] for table in table_id}
        if game in table_dict:
            table_id = table_dict[game]
            # 更新表格数据
            fs_table.update_table(table_id, records)
        else:
            print(f"Table for game '{game}' does not exist.")



@app.task(ignore_result=True, bind=True, max_retries=3)
def generate_article_tags_task(self, article_id):
    """异步任务：根据文章内容生成标签并更新文章"""
    try:
        article = Article.objects.get(id=article_id)
    except Article.DoesNotExist:
        logger.error(f"Article with id {article_id} does not exist.")
        return

    # 准备文章数据，使用JSON格式
    article_data = {
        "title": article.title,
        "summary": article.summary,
        "content": article.content
    }

    try:
        # 使用LLM_api类进行标签生成
        llm_api = LLM_api()
        generated_tags = llm_api.article_to_tags(article_data)
        if generated_tags:
            article.tags = generated_tags
            article.save(update_fields=['tags']) # 只更新 tags 字段
            logger.info(f"成功为文章 {article_id} 生成并更新标签: {generated_tags}")
        else:
            logger.info(f"文章 {article_id} 生成的标签无效或为空，不更新: {generated_tags}")

    except Exception as e:
        logger.error(f"为文章 {article_id} 生成标签时出错: {e}", exc_info=True)
        # 抛出异常以便Celery自动重试
        raise self.retry(exc=e, countdown=int(60 * (2 ** self.request.retries)))


@app.task(bind=True, max_retries=3, ignore_result=True)
def task__kcs_auto_link(self, session_id, min_confidence=0.8, max_links=2, creator_id=1):
    """
    根据会话内容自动关联相关文章
    
    :param session_id: 会话ID
    :param min_confidence: 置信度阈值，高于此阈值的文章将被关联
    :param max_links: 最多关联的文章数量
    :param creator_id: 关联操作的创建者ID，默认为superadmin(ID=1)
    """
    import logging
    from apps.kcs.utils.kcs_auto_link import process_session_for_autolink
    
    logger = logging.getLogger(__name__)
    
    try:
        logger.info(f"开始处理会话 {session_id} 的自动关联任务")
        
        # 调用自动关联处理函数
        result = process_session_for_autolink(
            session_id=session_id,
            min_confidence=min_confidence,
            max_links=max_links
        )
        
        if result:
            logger.info(f"会话 {session_id} 的自动关联任务成功完成")
        else:
            logger.info(f"会话 {session_id} 的自动关联任务未产生关联")
            
        return result
        
    except Exception as e:
        logger.error(f"自动关联任务出错: {str(e)}", exc_info=True)
    


@app.task(bind=True, max_retries=3, ignore_result=True)
def task__ai_cs_performance_analysis(self, link_ids, session_id, session_data):
    """
    异步任务：调用 LLM 分析客服在指定会话中的表现，并将结果存入数据库。

    :param link_ids:
    :param session_id: 会话 ID。
    :param session_data: 会话内容列表 (格式: [{"speaker": "q/a", "text": "..."}, ...])。
    """
    try:
        ai_cs_performance_analysis(link_ids, session_id, session_data)
        return "success"
    except Exception as e:
        logger.error(f"AI 客服表现分析任务失败: {str(e)}", exc_info=True)
        return "failed"


@app.task
def task__ai_cs_emotion_analysis(*args, **kwargs):
    """
    客服会话情绪分析异步任务

    Args:
        session_id: 会话ID
        force_reprocess: 是否强制重新处理
    """
    import logging
    from apps.kcs.utils.ai_cs_emotion_analysis import process_session_for_emotion_analysis

    logger = logging.getLogger(__name__)

    try:
        logger.info(f"[Task] 开始处理会话的情绪分析任务")
        session_id = kwargs.get('session_id')
        force_reprocess = kwargs.get('force_reprocess', False)

        if not session_id:
            logger.info(f"[Task] 会话ID为空，跳过情绪分析任务")
            return
        
        # 调用情绪分析处理函数
        success = process_session_for_emotion_analysis(session_id, force_reprocess)

        if success:
            logger.info(f"[Task] 会话情绪分析任务完成: {session_id}")
        else:
            logger.info(f"[Task] 会话情绪分析任务未执行（可能已处理或数据不足）: {session_id}")

    except Exception as e:
        logger.error(f"[Task] 会话情绪分析任务失败: {str(e)}", exc_info=True)