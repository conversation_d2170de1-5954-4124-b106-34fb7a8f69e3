"""
客服智能质检模块

该模块实现了基于会话内容对客服进行智能质检的功能。
流程如下:
1. MQ消费者接收会话关闭消息
2. 触发异步任务进行处理
3. 获取会话内容并格式化为Q&A格式
4. 通过LLM进行客服质检分析
5. 将质检结果保存到数据库
6. 支持质检结果的查询和展示
"""

import json
import logging
from json import JSONDecodeError
import re
from datetime import datetime
from django.db import transaction
from apps.common.LLM_api import LLM_api

logger = logging.getLogger(__name__)

def _parse_duration_safely(duration_str: str) -> int:
    """
    安全解析时长字符串为秒数
    
    Args:
        duration_str: 时长字符串，格式如 "01:23:45" 或可能包含空值
        
    Returns:
        int: 总秒数，解析失败时返回0
    """
    try:
        if not duration_str or duration_str.strip() == '':
            return 0
        
        # 分割时间字符串并过滤空值
        time_parts = [part.strip() for part in duration_str.split(':') if part.strip()]
        
        # 确保有3个部分（时:分:秒），不足的补0
        while len(time_parts) < 3:
            time_parts.insert(0, '0')
        
        # 只取前3个部分，防止格式异常
        time_parts = time_parts[:3]
        
        # 转换为整数并计算总秒数
        return sum(x * int(t) for x, t in zip([3600, 60, 1], time_parts))
        
    except (ValueError, AttributeError) as e:
        logger.warning(f"[QC] 时长解析失败: {duration_str}, 错误: {e}")
        return 0

def _clean_json_string(json_str: str) -> str:
    """
    清理JSON字符串中的控制字符和格式问题
    
    Args:
        json_str: 原始JSON字符串
        
    Returns:
        str: 清理后的JSON字符串
    """
    if not json_str:
        return json_str
    
    # 移除常见的控制字符，但保留必要的换行和制表符
    # 移除\x00-\x1f范围内的控制字符，但保留\t(09), \n(0A), \r(0D)
    cleaned = re.sub(r'[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]', '', json_str)
    
    # 处理可能的编码问题
    try:
        # 尝试编码解码来处理特殊字符
        cleaned = cleaned.encode('utf-8', errors='ignore').decode('utf-8')
    except (UnicodeEncodeError, UnicodeDecodeError):
        pass
    
    return cleaned

def _should_process_by_evaluation(session_evaluation: str, threshold: int) -> bool:
    """
    根据用户评价和阈值判断是否需要进行质检
    
    Args:
        session_evaluation: 会话评价值 (可能为空字符串或数字字符串)
        threshold: 质检阈值 (100=所有, 75=满意及以下, 50=一般及以下, 25=不满意及以下, 1=非常不满意)
        
    Returns:
        bool: 是否需要质检
    """
    # 阈值为100时，处理所有会话
    if threshold >= 100:
        return True
    
    # 如果没有评价，根据策略决定是否处理
    if not session_evaluation or session_evaluation.strip() == '':
        # 未评价的会话，只有在阈值为100时才处理
        return threshold >= 100
    
    try:
        # 尝试转换评价为数字
        eval_score = int(session_evaluation)
        
        # 根据阈值判断是否需要质检
        # 阈值越低，只质检评价越差的会话
        return eval_score <= threshold
        
    except (ValueError, TypeError):
        # 评价值无法转换为数字，按未评价处理
        logger.warning(f"[QC] 无法解析评价值: {session_evaluation}")
        return threshold >= 100

def _is_robot_service(service_info: dict) -> bool:
    """
    判断是否为机器人客服
    
    Args:
        service_info: 客服信息字典
        
    Returns:
        bool: 是否为机器人客服
    """
    service_id = service_info.get('service_id', '')
    service_name = service_info.get('service_name', '')
    
    # 检查service_id是否为负数（机器人标识）
    try:
        if service_id and int(service_id) < 0:
            return True
    except (ValueError, TypeError):
        pass
    
    # 检查service_name是否包含机器人关键词
    robot_keywords = ['机器人', 'robot', 'bot', '自动', 'auto']
    service_name_lower = service_name.lower()
    
    for keyword in robot_keywords:
        if keyword in service_name_lower:
            return True
    
    return False

def _get_qiyu_session_detail_for_qc(session_id):
    """
    获取会话详情并格式化为质检专用的Q&A格式
    优化数据格式，减少无用信息传输
    
    Args:
        session_id: 会话ID
        
    Returns:
        dict: 包含qa_list和客服信息的字典
    """
    from apps.common.qiyu_service import get_qiyu_service
    service = get_qiyu_service()
    # 获取会话基本信息
    session_detail = service.get_session_detail(session_id)
    if not session_detail:
        logger.warning(f"[QC] 会话 {session_id} 详情获取失败")
        return None
    
    # 获取客服信息
    service_info = {
        'service_id': session_detail.get('staffId'),
        'service_name': session_detail.get('staffName', '未知客服'),
        'service_account': session_detail.get('staffAccount', ''),
        'session_start_time': session_detail.get('startTime'),
        'session_end_time': session_detail.get('endTime'),
        'session_duration': _parse_duration_safely(session_detail.get('staffReceptionDuration', '00:00:00'))
    }
    
    # 获取会话消息
    messages = service.get_session_messages(session_id)
    if not messages:
        logger.warning(f"[QC] 会话 {session_id} 消息获取失败")
        return None
    
    # 格式化为简洁的Q&A格式，包含时间戳用于分析应答时长
    qa_list = []
    for message in messages:
        # 跳过自动回复
        if message.get("autoReply") == 1:
            continue
            
        msg_content = message.get("msg", "")
        msg_time = message.get("time", 0)
        
        # 处理JSON格式的消息内容
        if isinstance(msg_content, str) and msg_content.startswith("{"):
            try:
                msg_dict = json.loads(msg_content)
                # 跳过系统命令消息
                if any(key in msg_dict for key in ["cmd", "tagList"]):
                    continue
                # 提取URL或其他有用信息
                if "url" in msg_dict:
                    msg_content = f"[链接] {msg_dict['url']}"
                elif "text" in msg_dict:
                    msg_content = msg_dict["text"]
            except JSONDecodeError:
                continue
        
        # 过滤无效消息
        if not msg_content or len(msg_content.strip()) == 0:
            continue
        if "人工" in msg_content or "转接" in msg_content:
            continue
        if "client?" in msg_content:
            continue
            
        # 格式化消息 - 简化格式，增加时间戳
        if message.get("from") == 0:  # 客服消息
            qa_list.append({
                "type": "service", 
                "content": msg_content.strip(),
                "time": msg_time,
                "index": len([m for m in qa_list if m.get("type") == "service"])
            })
        else:  # 用户消息
            qa_list.append({
                "type": "user", 
                "content": msg_content.strip(),
                "time": msg_time
            })
    
    # 按时间排序
    qa_list.sort(key=lambda x: x.get('time', 0))
    
    return {
        'service_info': service_info,
        'qa_list': qa_list,
        'total_messages': len(qa_list),
        'evaluation': session_detail.get('evaluation', '')
    }

def _get_qiyu_session_game_id_for_qc(session_id):
    """
    从会话详情中提取游戏ID (复用auto_link的逻辑)
    
    Args:
        session_id: 会话ID
        
    Returns:
        Game.id 或 None
    """
    from apps.common.qiyu_service import get_qiyu_service
    from apps.kcs.models import Game
    from django.core.exceptions import ObjectDoesNotExist
    
    service = get_qiyu_service()
    detail = service.get_session_detail(session_id)
    if not detail:
        return None

    extraction_rules = [
        ("foreignId", "#", -1),
        ("fromPage", "/", -1), 
        ("landPage", "/", -1)
    ]

    try:
        for field, separator, position in extraction_rules:
            if field_value := detail.get(field):
                parts = field_value.split(separator)
                if len(parts) > abs(position):
                    game_id = int(parts[position])
                    return Game.objects.get(game_id=game_id).id

        if user_crm_info := detail.get("userCrmInfo"):
            user_crm_info = json.loads(user_crm_info)
            for item in user_crm_info:
                if item.get("key") == "game_id":
                    game_id = int(item["value"])
                    return Game.objects.get(game_id=game_id).id

    except (ValueError, IndexError, ObjectDoesNotExist) as e:
        logger.info(f"[QC] 会话 {session_id} 获取游戏ID失败: {e}")
    
    return None

def process_session_for_qc(session_id: str or int, force_reprocess: bool = False, evaluation: int = 100) -> bool:
    """
    处理会话进行客服质检的完整流程
    
    Args:
        session_id: 会话ID
        force_reprocess: 是否强制重新处理已处理过的会话
        evaluation: 质检评价阈值 (100=所有会话, 75=满意及以下, 50=一般及以下, 25=不满意及以下, 1=非常不满意)
        
    Returns:
        bool: 处理是否成功
    """
    from apps.cs_manage.models import CsQualityCheck
    
    try:
        logger.info(f"[QC] 开始处理会话 {session_id} 的质检")
        
        # 1. 检查是否已处理过
        if not force_reprocess and CsQualityCheck.objects.filter(session_id=session_id).exists():
            logger.info(f"[QC] 会话 {session_id} 已处理过质检，跳过")
            return False
        
        # 2. 获取会话数据
        session_data = _get_qiyu_session_detail_for_qc(session_id)
        
        if not session_data:
            logger.warning(f"[QC] 会话 {session_id} 数据获取失败")
            return False
        
        # 3. 检查评价阈值过滤
        session_evaluation = session_data.get('evaluation', '')
        if not _should_process_by_evaluation(session_evaluation, evaluation):
            logger.info(f"[QC] 会话 {session_id} 评价 {session_evaluation} 不满足阈值 {evaluation}，跳过质检")
            return False
            
        game_id = _get_qiyu_session_game_id_for_qc(session_id)
        service_info = session_data['service_info']
        qa_list = session_data['qa_list']
        
        # 4. 数据验证
        if not service_info.get('service_id'):
            logger.warning(f"[QC] 会话 {session_id} 缺少客服信息")
            return False
        
        # 5. 检查是否为机器人客服，机器人不进行质检
        if _is_robot_service(service_info):
            logger.info(f"[QC] 会话 {session_id} 为机器人客服 {service_info.get('service_name')}，跳过质检")
            return False
            
        # 检查至少有一轮对话
        if len(qa_list) < 2:
            logger.info(f"[QC] 会话 {session_id} 无双方回复，跳过质检")
            return False
        
        # 6. 调用LLM进行质检分析
        llm_api = LLM_api()
        
        # 准备LLM输入数据 - 简化格式，只传递必要信息
        llm_input = {
            'session_id': session_id,
            'service_name': service_info.get('service_name'),
            'conversation': qa_list,
            'game_id': game_id
        }
        
        # 调用LLM质检分析接口
        llm_response = llm_api.analyze_cs_quality(llm_input)
        
        if not llm_response:
            logger.warning(f"[QC] 会话 {session_id} LLM质检分析失败")
            return False
        
        # 解析LLM返回的JSON字符串
        try:
            # 清理JSON字符串中的控制字符
            cleaned_response = _clean_json_string(llm_response)
            qc_result = json.loads(cleaned_response)
            
            # 检查是否返回错误
            if 'error' in qc_result:
                logger.warning(f"[QC] 会话 {session_id} LLM返回错误: {qc_result['error']}")
                return False
                
        except json.JSONDecodeError as e:
            logger.error(f"[QC] 会话 {session_id} LLM返回格式错误: {e}, 原始内容长度: {len(llm_response)}")
            # 记录部分内容用于调试，避免日志过长
            preview = llm_response[:500] + "..." if len(llm_response) > 500 else llm_response
            logger.error(f"[QC] 内容预览: {preview}")
            return False
        
        # 7. 保存质检结果
        success = _save_qc_result(session_id, service_info, qc_result, game_id)
        
        if success:
            logger.info(f"[QC] 会话 {session_id} 质检处理完成，得分: {qc_result.get('overall_score', 0)}")
            return True
        else:
            logger.error(f"[QC] 会话 {session_id} 质检结果保存失败")
            return False
            
    except Exception as e:
        logger.error(f"[QC] 处理会话 {session_id} 质检时发生错误: {str(e)}", exc_info=True)
        return False

def _save_qc_result(session_id: str, service_info: dict, qc_result: dict, game_id: int = None) -> bool:
    """
    保存质检结果到数据库
    
    Args:
        session_id: 会话ID
        service_info: 客服信息
        qc_result: LLM质检结果
        game_id: 游戏ID
        
    Returns:
        bool: 保存是否成功
    """
    from apps.cs_manage.models import CsQualityCheck
    from apps.cs_manage.models import CsQualityDeduction
    
    try:
        with transaction.atomic():
            # 计算实际的总扣分（累加各扣分项）
            deduction_items = qc_result.get('deduction_items', [])
            calculated_total_deductions = sum(item.get('deduction_score', 0) for item in deduction_items)
            
            # 创建主质检记录
            qc_record = CsQualityCheck.objects.create(
                session_id=session_id,
                service_id=service_info.get('service_id'),
                service_name=service_info.get('service_name'),
                service_account=service_info.get('service_account'),
                game_id=game_id,
                overall_score=qc_result.get('overall_score', 0),
                total_deductions=calculated_total_deductions,  # 使用计算出的累加值
                analysis_summary=qc_result.get('analysis_summary', ''),
                service_performance=json.dumps(qc_result.get('service_performance', {})),
                session_start_time=datetime.fromtimestamp(service_info.get('session_start_time', 0) / 1000) if service_info.get('session_start_time') else None,
                session_end_time=datetime.fromtimestamp(service_info.get('session_end_time', 0) / 1000) if service_info.get('session_end_time') else None,
                session_duration=service_info.get('session_duration', 0)
            )
            
            # 创建扣分项记录
            for item in deduction_items:
                CsQualityDeduction.objects.create(
                    quality_check=qc_record,
                    item_name=item.get('item_name', ''),
                    category=item.get('category', ''),
                    severity=item.get('severity', ''),
                    description=item.get('description', ''),
                    deduction_score=item.get('deduction_score', 0),
                    message_index=item.get('message_index'),
                    message_content=item.get('message_content', ''),
                    suggestion=item.get('suggestion', '')
                )
            
            logger.info(f"[QC] 会话 {session_id} 质检结果保存成功，总分: {qc_record.overall_score}，总扣分: {calculated_total_deductions}")
            return True
            
    except Exception as e:
        logger.error(f"[QC] 保存会话 {session_id} 质检结果时出错: {str(e)}", exc_info=True)
        return False

def get_service_qc_summary(service_id: str = None, game_id: int = None, days: int = 30) -> dict:
    """
    获取客服质检汇总信息
    
    Args:
        service_id: 客服ID (可选)
        game_id: 游戏ID (可选)
        days: 统计天数
        
    Returns:
        dict: 汇总信息
    """
    from apps.cs_manage.models import CsQualityCheck
    from django.utils import timezone
    from django.db.models import Avg, Count, Sum
    from datetime import timedelta
    
    # 构建查询条件
    queryset = CsQualityCheck.objects.filter(
        created_time__gte=timezone.now() - timedelta(days=days)
    )
    
    if service_id:
        queryset = queryset.filter(service_id=service_id)
    if game_id:
        queryset = queryset.filter(game_id=game_id)
    
    # 统计数据
    summary = queryset.aggregate(
        total_sessions=Count('id'),
        avg_score=Avg('overall_score'),
        total_deductions=Sum('total_deductions'),
        avg_duration=Avg('session_duration')
    )
    
    # 按客服分组统计
    service_stats = queryset.values('service_id', 'service_name').annotate(
        session_count=Count('id'),
        avg_score=Avg('overall_score'),
        total_deductions=Sum('total_deductions'),
        avg_duration=Avg('session_duration')
    ).order_by('-avg_score')
    
    return {
        'summary': summary,
        'service_stats': list(service_stats),
        'period_days': days
    }

def get_service_qc_details(service_id: str, page: int = 1, page_size: int = 20) -> dict:
    """
    获取特定客服的详细质检记录
    
    Args:
        service_id: 客服ID
        page: 页码
        page_size: 每页数量
        
    Returns:
        dict: 详细记录
    """
    from apps.cs_manage.models import CsQualityCheck
    from django.core.paginator import Paginator
    
    queryset = CsQualityCheck.objects.filter(
        service_id=service_id
    ).select_related().prefetch_related('deductions').order_by('-create_datetime')
    
    paginator = Paginator(queryset, page_size)
    page_obj = paginator.get_page(page)
    
    records = []
    for qc in page_obj:
        # 解析service_performance JSON
        try:
            service_performance = json.loads(qc.service_performance) if qc.service_performance else {}
        except (json.JSONDecodeError, TypeError):
            service_performance = {}
            
        records.append({
            'id': qc.id,
            'session_id': qc.session_id,
            'overall_score': qc.overall_score,
            'total_deductions': qc.total_deductions,
            'analysis_summary': qc.analysis_summary,
            'create_datetime': qc.create_datetime,
            'session_duration': qc.session_duration,
            'service_performance': service_performance,
            'deduction_count': qc.deductions.count(),
            'deductions': [
                {
                    'item_name': d.item_name,
                    'category': d.category,
                    'severity': d.severity,
                    'description': d.description,
                    'deduction_score': d.deduction_score,
                    'message_content': d.message_content,
                    'suggestion': d.suggestion
                } for d in qc.deductions.all()
            ]
        })
    
    return {
        'records': records,
        'total_count': paginator.count,
        'page_count': paginator.num_pages,
        'current_page': page,
        'has_next': page_obj.has_next(),
        'has_previous': page_obj.has_previous()
    }
