"""
客服质检API视图 - cs_manage版本
提供质检数据的查询、统计和展示功能
"""

import json
import logging
from rest_framework import status
from rest_framework.decorators import action
from rest_framework.response import Response
from django.db.models import Q, Avg, <PERSON>, <PERSON><PERSON>, Max
from django.utils import timezone
from datetime import timedel<PERSON>, datetime
from dvadmin.utils.viewset import CustomModelViewSet
from dvadmin.utils.json_response import DetailResponse, SuccessResponse, ErrorResponse
from apps.cs_manage.models import CsQualityCheck, CsQualityDeduction
from apps.cs_manage.utils.LLM_auto_qc import get_service_qc_summary, get_service_qc_details, process_session_for_qc
from apps.cs_manage.serializers.ai_cs_qc import (
    CsQcExportSerializer, 
    CsQcListSerializer, 
    CsQcDetailSerializer
)

logger = logging.getLogger(__name__)


class CsQualityCheckViewSet(CustomModelViewSet):
    """
    客服质检记录视图集 - cs_manage版本
    """
    queryset = CsQualityCheck.objects.all().select_related('game').prefetch_related('deductions')
    ordering = ['-create_datetime']
    
    # 配置导出功能
    export_field_label = {
        'session_id': '会话ID',
        'service_name': '客服姓名', 
        'service_id': '客服ID',
        'service_account': '客服账号',
        'game_name': '游戏',
        'overall_score': '总分',
        'total_deductions': '总扣分',
        'session_duration': '会话时长(秒)',
        'session_start_time_str': '会话开始时间',
        'session_end_time_str': '会话结束时间',
        'create_datetime_str': '质检时间',
        'analysis_summary': '分析摘要',
        'deductions_text': '扣分详情',
        'service_performance_text': '客服表现'
    }
    export_serializer_class = CsQcExportSerializer
    
    # 配置不同操作的序列化器
    list_serializer_class = CsQcListSerializer
    retrieve_serializer_class = CsQcDetailSerializer

    @action(detail=False, methods=['get'])
    def dashboard(self, request):
        """
        质检看板数据
        返回客服质检的汇总统计信息
        """
        try:
            # 获取查询参数
            days = int(request.query_params.get('days', 1))
            game_id = request.query_params.get('game_id')
            service_id = request.query_params.get('service_id')
            
            # 构建查询条件
            end_date = timezone.now()
            if days == 0:  # 今日
                start_date = end_date.replace(hour=0, minute=0, second=0, microsecond=0)
                end_date = start_date + timedelta(days=1)
            elif days == 1:  # 昨日
                today_start = end_date.replace(hour=0, minute=0, second=0, microsecond=0)
                start_date = today_start - timedelta(days=1)
                end_date = today_start
            else:  # 最近N天
                start_date = end_date - timedelta(days=days)
            
            queryset = CsQualityCheck.objects.filter(
                create_datetime__gte=start_date,
                create_datetime__lt=end_date
            )
            
            if game_id:
                queryset = queryset.filter(game_id=game_id)
            if service_id:
                queryset = queryset.filter(service_id=service_id)
            
            # 总体统计
            total_stats = queryset.aggregate(
                total_sessions=Count('id'),
                avg_score=Avg('overall_score'),
                total_deductions=Sum('total_deductions'),
                avg_duration=Avg('session_duration')
            )
            
            # 客服排行榜 (按平均分排序)
            service_ranking = queryset.values(
                'service_id', 'service_name'
            ).annotate(
                session_count=Count('id'),
                avg_score=Avg('overall_score'),
                total_deductions=Sum('total_deductions'),
                avg_duration=Avg('session_duration')
            ).order_by('-avg_score')
            
            # 分数分布统计
            score_distribution = {
                'excellent': queryset.filter(overall_score__gte=90).count(),  # 优秀 90-100
                'good': queryset.filter(overall_score__gte=80, overall_score__lt=90).count(),  # 良好 80-89
                'average': queryset.filter(overall_score__gte=70, overall_score__lt=80).count(),  # 一般 70-79
                'poor': queryset.filter(overall_score__lt=70).count()  # 较差 <70
            }
            
            # 扣分项统计 (Top 10) - 使用cs_manage的扣分项
            deduction_stats = CsQualityDeduction.objects.filter(
                quality_check__in=queryset
            ).values('item_name').annotate(
                count=Count('id'),
                total_deduction=Sum('deduction_score'),
                avg_deduction=Avg('deduction_score')
            ).order_by('-count')[:10]
            
            # 时间趋势统计
            daily_stats = []
            if days <= 1:  # 今日或昨日，按小时统计
                for i in range(24):
                    hour_start = start_date + timedelta(hours=i)
                    hour_end = hour_start + timedelta(hours=1)
                    
                    hour_data = queryset.filter(
                        create_datetime__gte=hour_start,
                        create_datetime__lt=hour_end
                    ).aggregate(
                        session_count=Count('id'),
                        avg_score=Avg('overall_score')
                    )
                    hour_data['date'] = hour_start.strftime('%H:00')
                    daily_stats.append(hour_data)
            else:  # 多天，按天统计
                for i in range(days):
                    day = start_date + timedelta(days=i)
                    day_start = day.replace(hour=0, minute=0, second=0, microsecond=0)
                    day_end = day_start + timedelta(days=1)
                    
                    day_data = queryset.filter(
                        create_datetime__gte=day_start,
                        create_datetime__lt=day_end
                    ).aggregate(
                        session_count=Count('id'),
                        avg_score=Avg('overall_score')
                    )
                    day_data['date'] = day.strftime('%Y-%m-%d')
                    daily_stats.append(day_data)
            
            # 游戏分布统计 - 修复游戏关联查询
            game_stats = queryset.filter(
                game_id__isnull=False
            ).values(
                'game_id'
            ).annotate(
                session_count=Count('id'),
                avg_score=Avg('overall_score')
            ).order_by('-session_count')
            
            # 手动获取游戏名称
            from apps.kcs.models import Game
            game_dict = {g.id: g.name for g in Game.objects.all()}
            for stat in game_stats:
                stat['game_name'] = game_dict.get(stat['game_id'], '未知游戏')
            
            return SuccessResponse(data={
                'total_stats': total_stats,
                'service_ranking': list(service_ranking),
                'score_distribution': score_distribution,
                'deduction_stats': list(deduction_stats),
                'daily_trend': daily_stats,
                'game_stats': list(game_stats),
                'period': {
                    'days': days,
                    'start_date': start_date.strftime('%Y-%m-%d'),
                    'end_date': end_date.strftime('%Y-%m-%d')
                }
            })
            
        except Exception as e:
            return ErrorResponse(msg=f"获取看板数据失败: {str(e)}")

    @action(detail=False, methods=['get'])
    def service_list(self, request):
        """
        客服列表及其质检统计
        """
        try:
            days = int(request.query_params.get('days', 30))
            game_id = request.query_params.get('game_id')
            page = int(request.query_params.get('page', 1))
            page_size = int(request.query_params.get('page_size', 20))
            
            # 构建查询条件
            end_date = timezone.now()
            if days == 0:  # 今日
                start_date = end_date.replace(hour=0, minute=0, second=0, microsecond=0)
                end_date = start_date + timedelta(days=1)
            elif days == 1:  # 昨日
                today_start = end_date.replace(hour=0, minute=0, second=0, microsecond=0)
                start_date = today_start - timedelta(days=1)
                end_date = today_start
            else:  # 最近N天
                start_date = end_date - timedelta(days=days)
            
            queryset = CsQualityCheck.objects.filter(
                create_datetime__gte=start_date,
                create_datetime__lt=end_date
            )
            
            if game_id:
                queryset = queryset.filter(game_id=game_id)
            
            # 按客服分组统计
            service_stats = queryset.values(
                'service_id', 'service_name', 'service_account'
            ).annotate(
                session_count=Count('id'),
                avg_score=Avg('overall_score'),
                total_deductions=Sum('total_deductions'),
                avg_duration=Avg('session_duration'),
                latest_session=Max('create_datetime')
            ).order_by('-avg_score')
            
            # 分页处理
            from django.core.paginator import Paginator
            paginator = Paginator(service_stats, page_size)
            page_obj = paginator.get_page(page)
            
            # 为每个客服添加详细统计
            service_list = []
            for service in page_obj:
                # 获取该客服的扣分项分布 - 使用cs_manage的扣分项
                service_deductions = CsQualityDeduction.objects.filter(
                    quality_check__service_id=service['service_id'],
                    quality_check__create_datetime__gte=start_date
                ).values('item_name').annotate(
                    count=Count('id'),
                    total_score=Sum('deduction_score')
                ).order_by('-count')[:5]
                
                # 计算质检等级
                avg_score = service['avg_score'] or 0
                if avg_score >= 90:
                    grade = 'A'
                    grade_text = '优秀'
                elif avg_score >= 80:
                    grade = 'B'
                    grade_text = '良好'
                elif avg_score >= 70:
                    grade = 'C'
                    grade_text = '一般'
                else:
                    grade = 'D'
                    grade_text = '待改进'
                
                service_list.append({
                    **service,
                    'grade': grade,
                    'grade_text': grade_text,
                    'top_deductions': list(service_deductions)
                })
            
            return SuccessResponse(data={
                'service_list': service_list,
                'pagination': {
                    'total_count': paginator.count,
                    'page_count': paginator.num_pages,
                    'current_page': page,
                    'page_size': page_size,
                    'has_next': page_obj.has_next(),
                    'has_previous': page_obj.has_previous()
                }
            })
            
        except Exception as e:
            return ErrorResponse(msg=f"获取客服列表失败: {str(e)}")

    @action(detail=False, methods=['get'])
    def service_detail(self, request):
        """
        获取特定客服的详细质检记录
        """
        try:
            service_id = request.query_params.get('service_id')
            if not service_id:
                return ErrorResponse(msg="缺少service_id参数")
            
            page = int(request.query_params.get('page', 1))
            page_size = int(request.query_params.get('page_size', 20))
            days = int(request.query_params.get('days', 30))
            
            # 使用工具函数获取详细记录
            result = get_service_qc_details(service_id, page, page_size)
            
            # 获取该客服的汇总统计
            summary = get_service_qc_summary(service_id=service_id, days=days)
            
            return SuccessResponse(data={
                'records': result['records'],
                'pagination': {
                    'total_count': result['total_count'],
                    'page_count': result['page_count'],
                    'current_page': result['current_page'],
                    'has_next': result['has_next'],
                    'has_previous': result['has_previous']
                },
                'summary': summary
            })
            
        except Exception as e:
            return ErrorResponse(msg=f"获取客服详情失败: {str(e)}")

    @action(detail=False, methods=['post'])
    def manual_check(self, request):
        """
        手动触发质检
        """
        try:
            session_id = request.data.get('session_id')
            if not session_id:
                return ErrorResponse(msg="缺少session_id参数")
            
            force_reprocess = request.data.get('force_reprocess', False)
            
            # 调用质检处理函数
            success = process_session_for_qc(session_id, force_reprocess)
            
            if success:
                return SuccessResponse(msg="质检处理成功")
            else:
                return ErrorResponse(msg="质检处理失败，请检查会话ID或查看日志")
                
        except Exception as e:
            return ErrorResponse(msg=f"手动质检失败: {str(e)}")


class CsQualityDeductionViewSet(CustomModelViewSet):
    """
    客服质检扣分项视图集 - cs_manage版本
    """
    queryset = CsQualityDeduction.objects.all()
    ordering = ['-create_datetime']

    @action(detail=False, methods=['get'])
    def statistics(self, request):
        """
        扣分项统计
        """
        try:
            days = int(request.query_params.get('days', 30))
            
            end_date = timezone.now()
            start_date = end_date - timedelta(days=days)
            
            # 扣分项统计
            deduction_stats = self.queryset.filter(
                create_datetime__gte=start_date
            ).values('item_name').annotate(
                count=Count('id'),
                total_deduction=Sum('deduction_score'),
                avg_deduction=Avg('deduction_score')
            ).order_by('-count')
            
            return SuccessResponse(data={
                'deduction_stats': list(deduction_stats),
                'period': {
                    'days': days,
                    'start_date': start_date.strftime('%Y-%m-%d'),
                    'end_date': end_date.strftime('%Y-%m-%d')
                }
            })
            
        except Exception as e:
            return ErrorResponse(msg=f"获取扣分项统计失败: {str(e)}")
