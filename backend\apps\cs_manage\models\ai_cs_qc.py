from django.db import models
from dvadmin.utils.models import CoreModel, table_prefix
from apps.kcs.models import Game

# 使用cs_manage的表前缀
table_prefix = table_prefix + "cs_manage_"

class CsQualityCheck(CoreModel):
    """客服质检主表"""
    session_id = models.CharField(max_length=512, verbose_name="会话ID", help_text="七鱼会话ID", db_index=True)
    service_id = models.CharField(max_length=255, verbose_name="客服ID", help_text="客服ID", db_index=True)
    service_name = models.CharField(max_length=255, verbose_name="客服姓名", help_text="客服姓名")
    service_account = models.CharField(max_length=255, verbose_name="客服账号", help_text="客服账号", null=True, blank=True)
    game = models.ForeignKey(Game, on_delete=models.SET_NULL, verbose_name="游戏", help_text="关联游戏", null=True, blank=True)
    
    # 质检结果
    overall_score = models.IntegerField(default=100, verbose_name="总体得分", help_text="质检总分(0-100)")
    total_deductions = models.IntegerField(default=0, verbose_name="总扣分", help_text="总扣分数")
    session_summary = models.TextField(verbose_name="会话总结", help_text="会话总结", blank=True)
    analysis_summary = models.TextField(verbose_name="分析摘要", help_text="LLM分析摘要", blank=True)
    service_performance = models.JSONField(null=True, blank=True, verbose_name="客服表现数据", help_text="客服表现的详细数据")
    
    # 会话信息
    session_start_time = models.DateTimeField(null=True, blank=True, verbose_name="会话开始时间", help_text="会话开始时间")
    session_end_time = models.DateTimeField(null=True, blank=True, verbose_name="会话结束时间", help_text="会话结束时间")
    session_duration = models.IntegerField(default=0, verbose_name="会话时长", help_text="会话时长(秒)")
    
    class Meta:
        db_table = table_prefix + "cs_quality_check"
        verbose_name = "客服质检记录"
        verbose_name_plural = verbose_name
        indexes = [
            models.Index(fields=['session_id']),
            models.Index(fields=['service_id']),
            models.Index(fields=['overall_score']),
            models.Index(fields=['create_datetime']),
            models.Index(fields=['game']),
        ]
        unique_together = ('session_id',)  # 每个会话只能有一条质检记录


class CsQualityDeduction(CoreModel):
    """客服质检扣分项"""
    
    quality_check = models.ForeignKey(
        CsQualityCheck, 
        on_delete=models.CASCADE, 
        related_name='deductions',
        verbose_name="质检记录"
    )
    item_name = models.CharField(max_length=100, verbose_name="扣分项名称")
    category = models.CharField(max_length=50, default='', verbose_name="扣分类别")
    severity = models.CharField(max_length=20, default='', verbose_name="严重程度")
    description = models.TextField(verbose_name="扣分描述")
    deduction_score = models.FloatField(default=0, verbose_name="扣分数")
    message_index = models.IntegerField(null=True, blank=True, verbose_name="消息索引")
    message_content = models.TextField(blank=True, verbose_name="问题消息内容")
    suggestion = models.TextField(blank=True, verbose_name="改进建议")
    
    class Meta:
        db_table = table_prefix + "cs_quality_deduction"
        verbose_name = "客服质检扣分项"
        verbose_name_plural = verbose_name
        ordering = ['-id']
