# Generated by Django 4.2.7 on 2025-07-21 16:59

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('kcs', '0011_remove_csqualitydeduction_creator_and_more'),
        ('cs_manage', '0003_remove_dailymetricgroup_emotion_score'),
    ]

    operations = [
        migrations.CreateModel(
            name='CsQualityCheck',
            fields=[
                ('id', models.BigAutoField(help_text='Id', primary_key=True, serialize=False, verbose_name='Id')),
                ('description', models.CharField(blank=True, help_text='描述', max_length=255, null=True, verbose_name='描述')),
                ('modifier', models.CharField(blank=True, help_text='修改人', max_length=255, null=True, verbose_name='修改人')),
                ('dept_belong_id', models.CharField(blank=True, help_text='数据归属部门', max_length=255, null=True, verbose_name='数据归属部门')),
                ('update_datetime', models.DateTimeField(auto_now=True, help_text='修改时间', null=True, verbose_name='修改时间')),
                ('create_datetime', models.DateTimeField(auto_now_add=True, help_text='创建时间', null=True, verbose_name='创建时间')),
                ('session_id', models.CharField(db_index=True, help_text='七鱼会话ID', max_length=512, verbose_name='会话ID')),
                ('service_id', models.CharField(db_index=True, help_text='客服ID', max_length=255, verbose_name='客服ID')),
                ('service_name', models.CharField(help_text='客服姓名', max_length=255, verbose_name='客服姓名')),
                ('service_account', models.CharField(blank=True, help_text='客服账号', max_length=255, null=True, verbose_name='客服账号')),
                ('overall_score', models.IntegerField(default=100, help_text='质检总分(0-100)', verbose_name='总体得分')),
                ('total_deductions', models.IntegerField(default=0, help_text='总扣分数', verbose_name='总扣分')),
                ('analysis_summary', models.TextField(blank=True, help_text='LLM分析摘要', verbose_name='分析摘要')),
                ('service_performance', models.JSONField(blank=True, help_text='客服表现的详细数据', null=True, verbose_name='客服表现数据')),
                ('session_start_time', models.DateTimeField(blank=True, help_text='会话开始时间', null=True, verbose_name='会话开始时间')),
                ('session_end_time', models.DateTimeField(blank=True, help_text='会话结束时间', null=True, verbose_name='会话结束时间')),
                ('session_duration', models.IntegerField(default=0, help_text='会话时长(秒)', verbose_name='会话时长')),
                ('creator', models.ForeignKey(db_constraint=False, help_text='创建人', null=True, on_delete=django.db.models.deletion.SET_NULL, related_query_name='creator_query', to=settings.AUTH_USER_MODEL, verbose_name='创建人')),
                ('game', models.ForeignKey(blank=True, help_text='关联游戏', null=True, on_delete=django.db.models.deletion.SET_NULL, to='kcs.game', verbose_name='游戏')),
            ],
            options={
                'verbose_name': '客服质检记录',
                'verbose_name_plural': '客服质检记录',
                'db_table': 'admin_cs_manage_cs_quality_check',
            },
        ),
        migrations.CreateModel(
            name='CsQualityDeduction',
            fields=[
                ('id', models.BigAutoField(help_text='Id', primary_key=True, serialize=False, verbose_name='Id')),
                ('modifier', models.CharField(blank=True, help_text='修改人', max_length=255, null=True, verbose_name='修改人')),
                ('dept_belong_id', models.CharField(blank=True, help_text='数据归属部门', max_length=255, null=True, verbose_name='数据归属部门')),
                ('update_datetime', models.DateTimeField(auto_now=True, help_text='修改时间', null=True, verbose_name='修改时间')),
                ('create_datetime', models.DateTimeField(auto_now_add=True, help_text='创建时间', null=True, verbose_name='创建时间')),
                ('item_name', models.CharField(max_length=100, verbose_name='扣分项名称')),
                ('category', models.CharField(default='', max_length=50, verbose_name='扣分类别')),
                ('severity', models.CharField(default='', max_length=20, verbose_name='严重程度')),
                ('description', models.TextField(verbose_name='扣分描述')),
                ('deduction_score', models.FloatField(default=0, verbose_name='扣分数')),
                ('message_index', models.IntegerField(blank=True, null=True, verbose_name='消息索引')),
                ('message_content', models.TextField(blank=True, verbose_name='问题消息内容')),
                ('suggestion', models.TextField(blank=True, verbose_name='改进建议')),
                ('creator', models.ForeignKey(db_constraint=False, help_text='创建人', null=True, on_delete=django.db.models.deletion.SET_NULL, related_query_name='creator_query', to=settings.AUTH_USER_MODEL, verbose_name='创建人')),
                ('quality_check', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='deductions', to='cs_manage.csqualitycheck', verbose_name='质检记录')),
            ],
            options={
                'verbose_name': '客服质检扣分项',
                'verbose_name_plural': '客服质检扣分项',
                'db_table': 'admin_cs_manage_cs_quality_deduction',
                'ordering': ['-id'],
            },
        ),
        migrations.AddIndex(
            model_name='csqualitycheck',
            index=models.Index(fields=['session_id'], name='admin_cs_ma_session_298299_idx'),
        ),
        migrations.AddIndex(
            model_name='csqualitycheck',
            index=models.Index(fields=['service_id'], name='admin_cs_ma_service_789d91_idx'),
        ),
        migrations.AddIndex(
            model_name='csqualitycheck',
            index=models.Index(fields=['overall_score'], name='admin_cs_ma_overall_21fb50_idx'),
        ),
        migrations.AddIndex(
            model_name='csqualitycheck',
            index=models.Index(fields=['create_datetime'], name='admin_cs_ma_create__03b370_idx'),
        ),
        migrations.AddIndex(
            model_name='csqualitycheck',
            index=models.Index(fields=['game'], name='admin_cs_ma_game_id_b9d6c0_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='csqualitycheck',
            unique_together={('session_id',)},
        ),
    ]
